// src/components/auth/auth-modal.tsx
'use client';

import { useEffect, useState } from 'react';
import AuthForm from './auth-form';

interface AuthModalProps {
  isOpen: boolean;
  onClose: () => void;
  defaultTab?: 'login' | 'signup';
}

const AuthModal = ({ isOpen, onClose, defaultTab = 'signup' }: AuthModalProps) => {
  const [activeTab, setActiveTab] = useState<'login' | 'signup'>(defaultTab);

  useEffect(() => {
    setActiveTab(defaultTab);
  }, [defaultTab, isOpen]);

  useEffect(() => {
    if (isOpen) {
      document.body.style.overflow = 'hidden';
    } else {
      document.body.style.overflow = 'unset';
    }

    return () => {
      document.body.style.overflow = 'unset';
    };
  }, [isOpen]);

  // Handle escape key and focus management
  useEffect(() => {
    const handleEscape = (e: KeyboardEvent) => {
      if (e.key === 'Escape' && isOpen) {
        onClose();
      }
    };

    if (isOpen) {
      document.addEventListener('keydown', handleEscape);

      // Focus the modal when it opens
      const modal = document.querySelector('[role="dialog"]') as HTMLElement;
      if (modal) {
        modal.focus();
      }

      // Trap focus within modal
      const focusableElements = modal?.querySelectorAll(
        'button, [href], input, select, textarea, [tabindex]:not([tabindex="-1"])'
      );

      if (focusableElements && focusableElements.length > 0) {
        const firstElement = focusableElements[0] as HTMLElement;
        const lastElement = focusableElements[focusableElements.length - 1] as HTMLElement;

        const handleTabKey = (e: KeyboardEvent) => {
          if (e.key === 'Tab') {
            if (e.shiftKey) {
              if (document.activeElement === firstElement) {
                e.preventDefault();
                lastElement.focus();
              }
            } else {
              if (document.activeElement === lastElement) {
                e.preventDefault();
                firstElement.focus();
              }
            }
          }
        };

        document.addEventListener('keydown', handleTabKey);

        return () => {
          document.removeEventListener('keydown', handleEscape);
          document.removeEventListener('keydown', handleTabKey);
        };
      }
    }

    return () => {
      document.removeEventListener('keydown', handleEscape);
    };
  }, [isOpen, onClose]);

  if (!isOpen) return null;

  const handleToggle = (tab: 'login' | 'signup') => {
    setActiveTab(tab);
  };

  const handleBackdropClick = (e: React.MouseEvent) => {
    if (e.target === e.currentTarget) {
      onClose();
    }
  };  return (
    <div
      className="fixed inset-0 bg-gradient-to-br from-slate-900/90 via-slate-800/90 to-slate-900/90 flex items-center justify-center p-3 sm:p-4 z-[70] animate-fadeIn font-sans backdrop-blur-md"
      onClick={handleBackdropClick}
      role="dialog"
      aria-modal="true"
      aria-labelledby="auth-modal-title"
      tabIndex={-1}
    >
      <div className="bg-white/98 backdrop-blur-sm rounded-2xl w-full max-w-sm sm:max-w-md mx-auto relative animate-slideIn shadow-2xl border border-white/30 ring-1 ring-slate-200/50 max-h-[95vh] sm:max-h-[90vh] overflow-hidden">
        {/* Close Button */}
        <button
          onClick={onClose}
          className="absolute top-3 right-3 text-slate-400 hover:text-slate-600 transition-all duration-200 p-2 z-20 hover:bg-slate-100/80 rounded-full backdrop-blur-sm"
          aria-label="Close authentication modal"
        >
          <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M6 18L18 6M6 6l12 12" />
          </svg>
        </button>

        {/* Modal Header */}
        <div className="px-4 sm:px-6 pt-5 sm:pt-6 pb-3 sm:pb-4 bg-gradient-to-br from-white/98 to-slate-50/98 backdrop-blur-sm border-b border-slate-200/50">
          <div className="text-center">
            {/* Logo and Brand */}
            <div className="flex items-center justify-center space-x-2 sm:space-x-2.5 mb-4 sm:mb-5">
              <div className="w-8 h-8 sm:w-9 sm:h-9 bg-gradient-to-br from-indigo-600 via-purple-600 to-indigo-700 rounded-xl flex items-center justify-center shadow-lg shadow-indigo-500/25">
                <svg className="w-4 h-4 sm:w-5 sm:h-5 text-white" fill="currentColor" viewBox="0 0 24 24">
                  <path d="M12 2L2 7v10c0 5.55 3.84 9.74 9 11 5.16-1.26 9-5.45 9-11V7l-10-5z"/>
                </svg>
              </div>
              <h2 id="auth-modal-title" className="text-lg-app sm:text-xl-app font-bold text-slate-900">EduPro</h2>
            </div>

            {/* Tab Toggle */}
            <div className="flex bg-slate-100/90 backdrop-blur-sm rounded-xl p-1 sm:p-1.5 shadow-inner border border-slate-200/50">
              <button
                onClick={() => handleToggle('signup')}
                type="button"
                className={`flex-1 py-2 sm:py-2.5 px-3 sm:px-4 rounded-lg text-xs-app sm:text-sm-app font-semibold transition-all duration-300 ${
                  activeTab === 'signup'
                    ? 'bg-gradient-to-r from-indigo-600 to-purple-600 text-white shadow-lg shadow-indigo-500/25 transform scale-[1.02]'
                    : 'text-slate-700 hover:text-slate-900 hover:bg-white/70'
                }`}
                aria-pressed={activeTab === 'signup'}
                aria-label="Switch to sign up form"
              >
                Sign Up
              </button>
              <button
                onClick={() => handleToggle('login')}
                type="button"
                className={`flex-1 py-2 sm:py-2.5 px-3 sm:px-4 rounded-lg text-xs-app sm:text-sm-app font-semibold transition-all duration-300 ${
                  activeTab === 'login'
                    ? 'bg-gradient-to-r from-indigo-600 to-purple-600 text-white shadow-lg shadow-indigo-500/25 transform scale-[1.02]'
                    : 'text-slate-700 hover:text-slate-900 hover:bg-white/70'
                }`}
                aria-pressed={activeTab === 'login'}
                aria-label="Switch to sign in form"
              >
                Sign In
              </button>
            </div>
          </div>
        </div>        {/* Modal Body */}
        <div className="px-4 sm:px-6 py-4 sm:py-5 overflow-y-auto bg-gradient-to-br from-indigo-50/60 via-purple-50/40 to-pink-50/60 backdrop-blur-sm relative max-h-[calc(95vh-160px)] sm:max-h-[calc(90vh-180px)]">
          {/* Subtle overlay pattern */}
          <div className="absolute inset-0 bg-gradient-to-tr from-blue-50/20 via-transparent to-violet-50/20 pointer-events-none"></div>
          <div className="relative z-10">
            <div className="text-center mb-4 sm:mb-5">
              <h3 className="text-base-app sm:text-lg-app font-bold text-slate-900 mb-1 sm:mb-2">
                {activeTab === 'signup' ? 'Create Your Account' : 'Welcome Back'}
              </h3>
              <p className="text-slate-600 text-xs-app sm:text-sm-app leading-relaxed px-2 sm:px-0">
                {activeTab === 'signup'
                  ? 'Join thousands of educators worldwide and unlock premium features'
                  : 'Sign in to continue your learning journey with EduPro'
                }
              </p>
            </div>
            <AuthForm formType={activeTab} onToggleForm={handleToggle} />
          </div>
        </div>
      </div>
    </div>
  );
};

export default AuthModal;

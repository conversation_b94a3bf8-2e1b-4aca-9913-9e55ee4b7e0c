// src/lib/supabase-test.ts
'use client';

import { supabase } from './supabase';

/**
 * Test Supabase connection and authentication functionality
 */
export class SupabaseTestService {
  /**
   * Test basic Supabase connection
   */
  static async testConnection(): Promise<{ success: boolean; error?: string; data?: any }> {
    try {
      console.log('Testing Supabase connection...');
      
      // Test basic connection by getting the current session
      const { data: { session }, error } = await supabase.auth.getSession();
      
      if (error) {
        console.error('Supabase connection error:', error);
        return {
          success: false,
          error: `Connection failed: ${error.message}`
        };
      }
      
      console.log('Supabase connection successful. Current session:', session);
      
      return {
        success: true,
        data: {
          connected: true,
          hasSession: !!session,
          sessionUser: session?.user?.email || null
        }
      };
    } catch (error) {
      console.error('Supabase connection test failed:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown connection error'
      };
    }
  }

  /**
   * Test Supabase environment configuration
   */
  static testEnvironmentConfig(): { success: boolean; error?: string; data?: any } {
    try {
      const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL;
      const supabaseAnonKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY;
      
      console.log('Testing environment configuration...');
      console.log('NEXT_PUBLIC_SUPABASE_URL:', supabaseUrl ? 'Set' : 'Missing');
      console.log('NEXT_PUBLIC_SUPABASE_ANON_KEY:', supabaseAnonKey ? 'Set' : 'Missing');
      
      if (!supabaseUrl) {
        return {
          success: false,
          error: 'NEXT_PUBLIC_SUPABASE_URL environment variable is missing'
        };
      }
      
      if (!supabaseAnonKey) {
        return {
          success: false,
          error: 'NEXT_PUBLIC_SUPABASE_ANON_KEY environment variable is missing'
        };
      }
      
      // Validate URL format
      try {
        new URL(supabaseUrl);
      } catch {
        return {
          success: false,
          error: 'NEXT_PUBLIC_SUPABASE_URL is not a valid URL'
        };
      }
      
      return {
        success: true,
        data: {
          supabaseUrl,
          anonKeyLength: supabaseAnonKey.length,
          urlValid: true
        }
      };
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown configuration error'
      };
    }
  }

  /**
   * Test authentication with test credentials
   */
  static async testAuthentication(email: string, password: string): Promise<{ success: boolean; error?: string; data?: any }> {
    try {
      console.log('Testing authentication with credentials:', { email });
      
      // First try to sign in
      const { data: signInData, error: signInError } = await supabase.auth.signInWithPassword({
        email,
        password
      });
      
      if (signInError) {
        console.log('Sign in failed, error:', signInError);
        
        // If user doesn't exist, try to sign up
        if (signInError.message.includes('Invalid login credentials') || 
            signInError.message.includes('User not found')) {
          
          console.log('Attempting to sign up new user...');
          
          const { data: signUpData, error: signUpError } = await supabase.auth.signUp({
            email,
            password,
            options: {
              data: {
                full_name: 'Test User',
                role: 'student'
              }
            }
          });
          
          if (signUpError) {
            console.error('Sign up failed:', signUpError);
            return {
              success: false,
              error: `Sign up failed: ${signUpError.message}`
            };
          }
          
          console.log('Sign up successful:', signUpData);
          
          return {
            success: true,
            data: {
              action: 'signup',
              user: signUpData.user,
              needsConfirmation: !signUpData.session
            }
          };
        }
        
        return {
          success: false,
          error: `Authentication failed: ${signInError.message}`
        };
      }
      
      console.log('Sign in successful:', signInData);
      
      return {
        success: true,
        data: {
          action: 'signin',
          user: signInData.user,
          session: signInData.session
        }
      };
    } catch (error) {
      console.error('Authentication test failed:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown authentication error'
      };
    }
  }

  /**
   * Run comprehensive Supabase tests
   */
  static async runComprehensiveTest(testEmail?: string, testPassword?: string): Promise<{ 
    success: boolean; 
    results: any; 
    summary: string 
  }> {
    console.log('🧪 Running comprehensive Supabase tests...');
    
    const results = {
      environment: { success: false, error: '', data: null },
      connection: { success: false, error: '', data: null },
      authentication: { success: false, error: '', data: null }
    };
    
    // Test 1: Environment Configuration
    console.log('1️⃣ Testing environment configuration...');
    results.environment = this.testEnvironmentConfig();
    
    // Test 2: Connection
    console.log('2️⃣ Testing Supabase connection...');
    results.connection = await this.testConnection();
    
    // Test 3: Authentication (if credentials provided)
    if (testEmail && testPassword) {
      console.log('3️⃣ Testing authentication...');
      results.authentication = await this.testAuthentication(testEmail, testPassword);
    } else {
      console.log('3️⃣ Skipping authentication test (no credentials provided)');
      results.authentication = { success: true, error: 'Skipped - no credentials provided', data: null };
    }
    
    // Generate summary
    const passedTests = Object.values(results).filter(r => r.success).length;
    const totalTests = Object.keys(results).length;
    const allPassed = passedTests === totalTests;
    
    const summary = `Tests completed: ${passedTests}/${totalTests} passed. ${
      allPassed ? '✅ All tests passed!' : '❌ Some tests failed.'
    }`;
    
    console.log('🏁 Test Summary:', summary);
    console.log('📊 Detailed Results:', results);
    
    return {
      success: allPassed,
      results,
      summary
    };
  }
}

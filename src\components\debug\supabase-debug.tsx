// src/components/debug/supabase-debug.tsx
'use client';

import { useState } from 'react';
import { SupabaseTestService } from '@/lib/supabase-test';

const SupabaseDebugPanel = () => {
  const [testResults, setTestResults] = useState<any>(null);
  const [isLoading, setIsLoading] = useState(false);
  const [testCredentials, setTestCredentials] = useState({
    email: '<EMAIL>',
    password: 'testpassword123'
  });

  const runTests = async () => {
    setIsLoading(true);
    try {
      const results = await SupabaseTestService.runComprehensiveTest(
        testCredentials.email,
        testCredentials.password
      );
      setTestResults(results);
    } catch (error) {
      console.error('Test execution failed:', error);
      setTestResults({
        success: false,
        results: { error: error instanceof Error ? error.message : 'Unknown error' },
        summary: 'Test execution failed'
      });
    } finally {
      setIsLoading(false);
    }
  };

  const runConnectionTest = async () => {
    setIsLoading(true);
    try {
      const result = await SupabaseTestService.testConnection();
      setTestResults({
        success: result.success,
        results: { connection: result },
        summary: result.success ? 'Connection test passed' : 'Connection test failed'
      });
    } catch (error) {
      console.error('Connection test failed:', error);
      setTestResults({
        success: false,
        results: { error: error instanceof Error ? error.message : 'Unknown error' },
        summary: 'Connection test failed'
      });
    } finally {
      setIsLoading(false);
    }
  };

  const runEnvironmentTest = () => {
    const result = SupabaseTestService.testEnvironmentConfig();
    setTestResults({
      success: result.success,
      results: { environment: result },
      summary: result.success ? 'Environment test passed' : 'Environment test failed'
    });
  };

  return (
    <div className="fixed bottom-4 right-4 bg-white border border-gray-300 rounded-lg shadow-lg p-4 max-w-md z-50">
      <div className="mb-4">
        <h3 className="text-lg font-bold text-gray-900 mb-2">🧪 Supabase Debug Panel</h3>
        
        <div className="space-y-2 mb-4">
          <button
            onClick={runEnvironmentTest}
            disabled={isLoading}
            className="w-full px-3 py-2 bg-blue-500 text-white rounded hover:bg-blue-600 disabled:opacity-50 text-sm"
          >
            Test Environment
          </button>
          
          <button
            onClick={runConnectionTest}
            disabled={isLoading}
            className="w-full px-3 py-2 bg-green-500 text-white rounded hover:bg-green-600 disabled:opacity-50 text-sm"
          >
            Test Connection
          </button>
          
          <div className="space-y-1">
            <input
              type="email"
              placeholder="Test email"
              value={testCredentials.email}
              onChange={(e) => setTestCredentials(prev => ({ ...prev, email: e.target.value }))}
              className="w-full px-2 py-1 border border-gray-300 rounded text-sm"
            />
            <input
              type="password"
              placeholder="Test password"
              value={testCredentials.password}
              onChange={(e) => setTestCredentials(prev => ({ ...prev, password: e.target.value }))}
              className="w-full px-2 py-1 border border-gray-300 rounded text-sm"
            />
          </div>
          
          <button
            onClick={runTests}
            disabled={isLoading}
            className="w-full px-3 py-2 bg-purple-500 text-white rounded hover:bg-purple-600 disabled:opacity-50 text-sm"
          >
            {isLoading ? 'Running Tests...' : 'Run All Tests'}
          </button>
        </div>
      </div>

      {testResults && (
        <div className="border-t pt-4">
          <h4 className="font-semibold text-gray-900 mb-2">Test Results:</h4>
          <div className={`p-2 rounded text-sm ${testResults.success ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'}`}>
            <p className="font-medium">{testResults.summary}</p>
          </div>
          
          <details className="mt-2">
            <summary className="cursor-pointer text-sm text-gray-600 hover:text-gray-800">
              View Details
            </summary>
            <pre className="mt-2 p-2 bg-gray-100 rounded text-xs overflow-auto max-h-40">
              {JSON.stringify(testResults.results, null, 2)}
            </pre>
          </details>
        </div>
      )}
    </div>
  );
};

export default SupabaseDebugPanel;
